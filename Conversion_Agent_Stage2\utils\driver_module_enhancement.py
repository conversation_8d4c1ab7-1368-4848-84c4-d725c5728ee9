"""
Driver Module Enhancement Utility for Stage 2 Conversion Analysis.

This module implements the driver module approach where multiple responsible modules
are combined into a single module for enhancement, then decomposed back with only
functionally changed modules saved as attempt files.
"""

import re
import os
from typing import Dict, List, Tuple, Any
from Conversion_Agent_Stage2.prompts.module_enhancement_prompt import create_module_enhancement_prompt
from Conversion_Agent_Stage2.state import ModuleEnhancementResponse


class DriverModuleEnhancer:
    """
    Handles the driver module enhancement approach for multiple responsible modules.
    """
    
    def __init__(self, llm_client):
        """
        Initialize the driver module enhancer.
        
        Args:
            llm_client: The LLM client for AI enhancement
        """
        self.llm_client = llm_client
    
    def combine_responsible_modules(self, responsible_modules: List[Tuple[str, str, str]]) -> Tuple[str, Dict[str, Dict[str, str]]]:
        """
        Combine all responsible modules into a single driver module.
        
        Args:
            responsible_modules: List of (feature_name, module_path, module_code) tuples
            
        Returns:
            Tuple of (combined_code, module_boundaries)
        """
        combined_code = ""
        module_boundaries = {}
        
        print(f"📦 Combining {len(responsible_modules)} modules into driver module")
        
        for feature_name, module_path, module_code in responsible_modules:
            # Create unique boundary markers
            start_marker = f"# === MODULE: {feature_name} START ==="
            end_marker = f"# === MODULE: {feature_name} END ==="
            
            # Add module to combined code with boundaries
            combined_code += f"\n{start_marker}\n"
            combined_code += module_code
            combined_code += f"\n{end_marker}\n"
            
            # Track boundaries for later decomposition
            module_boundaries[feature_name] = {
                'start_marker': start_marker,
                'end_marker': end_marker,
                'module_path': module_path
            }
            
            print(f"   📋 Added module: {feature_name}")
        
        return combined_code, module_boundaries
    
    def enhance_combined_module(self, combined_code: str, ai_context: Dict[str, Any]) -> str:
        """
        Send combined module to AI for enhancement.
        
        Args:
            combined_code: The combined module code
            ai_context: Context for AI enhancement
            
        Returns:
            Enhanced combined module code
        """
        print("🤖 Starting AI enhancement of combined module")
        
        # Create enhancement prompt for combined module
        prompt = create_module_enhancement_prompt(
            original_module_code=combined_code,
            qmigrator_target_statement=ai_context.get('current_target_output', ''),
            ai_corrected_statement=ai_context.get('ai_corrected_output', ''),
            deployment_error=ai_context.get('deployment_error', ''),
            ai_comparison_feedback=ai_context.get('ai_comparison_feedback'),
            attempt_history=ai_context.get('attempt_history'),
            migration_name=ai_context.get('migration_name'),
            feature_name="combined_driver_module",
            keywords=ai_context.get('keywords', ''),
            db_terms=ai_context.get('db_terms')
        )
        
        # Add specific instruction for combined module
        combined_prompt = f"""
{prompt}

IMPORTANT: You are working with a COMBINED MODULE containing multiple functions.
- Preserve all module boundary markers exactly as they are
- Only modify the functional code that needs changes to achieve the transformation
- Do not remove or modify the boundary markers: # === MODULE: ... START/END ===
- Focus on the specific transformation needed: current output → AI corrected output
"""
        
        try:
            # Call AI for enhancement using structured output
            response = self.llm_client.with_structured_output(ModuleEnhancementResponse).invoke(combined_prompt)

            print("✅ AI enhancement completed successfully")
            return response.enhanced_code
            
        except Exception as e:
            print(f"❌ AI enhancement failed: {str(e)}")
            return combined_code  # Return original if enhancement fails
    
    def extract_module_from_combined(self, enhanced_combined_code: str, module_info: Dict[str, str]) -> str:
        """
        Extract a specific module from the enhanced combined code.
        
        Args:
            enhanced_combined_code: The enhanced combined module code
            module_info: Module boundary information
            
        Returns:
            Extracted module code
        """
        start_marker = module_info['start_marker']
        end_marker = module_info['end_marker']
        
        # Find the module content between markers
        start_pattern = re.escape(start_marker)
        end_pattern = re.escape(end_marker)
        
        pattern = f"{start_pattern}(.*?){end_pattern}"
        match = re.search(pattern, enhanced_combined_code, re.DOTALL)
        
        if match:
            module_content = match.group(1).strip()
            return module_content
        else:
            print(f"⚠️ Could not extract module with markers: {start_marker}")
            return ""
    
    def has_functional_changes(self, original_code: str, enhanced_code: str) -> bool:
        """
        Detect if there are actual functional changes (ignore comments/formatting).
        
        Args:
            original_code: Original module code
            enhanced_code: Enhanced module code
            
        Returns:
            True if functional changes detected, False otherwise
        """
        def normalize_functional_code(code: str) -> str:
            """Remove comments and normalize whitespace for comparison."""
            lines = []
            for line in code.split('\n'):
                # Remove full-line comments
                if line.strip().startswith('#'):
                    continue
                # Remove inline comments
                line = re.sub(r'#.*$', '', line)
                # Normalize whitespace
                line = ' '.join(line.split())
                if line:
                    lines.append(line)
            return '\n'.join(lines)
        
        original_functional = normalize_functional_code(original_code)
        enhanced_functional = normalize_functional_code(enhanced_code)
        
        return original_functional != enhanced_functional
    
    def decompose_and_detect_changes(self, base_modules: Dict[str, str], enhanced_combined_code: str,
                                   module_boundaries: Dict[str, Dict[str, str]]) -> Dict[str, Dict[str, Any]]:
        """
        Decompose enhanced combined code and detect functional changes.

        Args:
            base_modules: Dict of {feature_name: base_code} (could be original or previous attempt)
            enhanced_combined_code: Enhanced combined module code
            module_boundaries: Module boundary information

        Returns:
            Dict of decomposition results for each module
        """
        print("🔍 Decomposing enhanced combined module and detecting changes")
        print(f"📊 Comparing against base modules (could be original or previous attempts)")
        
        results = {}
        
        for feature_name, base_code in base_modules.items():
            if feature_name in module_boundaries:
                # Extract enhanced module from combined code
                enhanced_module_code = self.extract_module_from_combined(
                    enhanced_combined_code, 
                    module_boundaries[feature_name]
                )
                
                if enhanced_module_code:
                    # Check for functional changes
                    has_changes = self.has_functional_changes(base_code, enhanced_module_code)
                    
                    results[feature_name] = {
                        'status': 'changed' if has_changes else 'unchanged',
                        'enhanced_code': enhanced_module_code if has_changes else None,
                        'action': 'create_attempt' if has_changes else 'use_original',
                        'module_path': module_boundaries[feature_name]['module_path']
                    }
                    
                    status_icon = "✅" if has_changes else "➖"
                    status_text = "Functional changes detected" if has_changes else "No functional changes"
                    print(f"   {status_icon} {feature_name}: {status_text}")
                else:
                    print(f"   ⚠️ {feature_name}: Could not extract from combined module")
                    results[feature_name] = {
                        'status': 'error',
                        'enhanced_code': None,
                        'action': 'use_original',
                        'module_path': module_boundaries[feature_name]['module_path']
                    }
            else:
                print(f"   ⚠️ {feature_name}: No boundary information found")
                results[feature_name] = {
                    'status': 'error',
                    'enhanced_code': None,
                    'action': 'use_original',
                    'module_path': ''
                }
        
        return results

    def save_decomposed_modules(self, decomposition_results: Dict[str, Dict[str, Any]],
                              feature_modules_dir: str, attempt_number: int) -> Dict[str, List[str]]:
        """
        Save only modules that have functional changes as attempt files.

        Args:
            decomposition_results: Results from decomposition and change detection
            feature_modules_dir: Directory to save attempt modules
            attempt_number: Current attempt number

        Returns:
            Dict with lists of saved and unchanged modules
        """
        print("💾 Saving decomposed modules")

        saved_modules = []
        unchanged_modules = []

        # Ensure feature modules directory exists
        os.makedirs(feature_modules_dir, exist_ok=True)

        for feature_name, result in decomposition_results.items():
            if result['action'] == 'create_attempt' and result['enhanced_code']:
                # Save as attempt module
                attempt_filename = f"{feature_name}_attempt_{attempt_number}.py"
                attempt_filepath = os.path.join(feature_modules_dir, attempt_filename)

                try:
                    with open(attempt_filepath, 'w', encoding='utf-8') as f:
                        f.write(result['enhanced_code'])

                    saved_modules.append(feature_name)
                    print(f"   ✅ {feature_name}: Saved as {attempt_filename}")

                except Exception as e:
                    print(f"   ❌ {feature_name}: Failed to save - {str(e)}")
                    unchanged_modules.append(feature_name)

            elif result['action'] == 'use_original':
                # For incremental learning, we still need to save the module as an attempt file
                # even if no functional changes detected, to maintain the learning chain
                attempt_filename = f"{feature_name}_attempt_{attempt_number}.py"
                attempt_filepath = os.path.join(feature_modules_dir, attempt_filename)

                # Get the base module code (could be from previous attempt)
                base_code = base_modules.get(feature_name, '')

                try:
                    with open(attempt_filepath, 'w', encoding='utf-8') as f:
                        f.write(base_code)

                    saved_modules.append(feature_name)
                    print(f"   ➖ {feature_name}: Saved as {attempt_filename} (no functional changes, but maintaining learning chain)")

                except Exception as e:
                    print(f"   ❌ {feature_name}: Failed to save - {str(e)}")
                    unchanged_modules.append(feature_name)
            else:
                # Error case - still save attempt file to maintain learning chain
                attempt_filename = f"{feature_name}_attempt_{attempt_number}.py"
                attempt_filepath = os.path.join(feature_modules_dir, attempt_filename)

                # Get the base module code (could be from previous attempt)
                base_code = base_modules.get(feature_name, '')

                try:
                    with open(attempt_filepath, 'w', encoding='utf-8') as f:
                        f.write(base_code)

                    saved_modules.append(feature_name)
                    print(f"   ⚠️ {feature_name}: Saved as {attempt_filename} (error case, but maintaining learning chain)")

                except Exception as e:
                    print(f"   ❌ {feature_name}: Failed to save - {str(e)}")
                    unchanged_modules.append(feature_name)

        return {
            'saved_modules': saved_modules,
            'unchanged_modules': unchanged_modules
        }

    def driver_module_enhancement_workflow(self, responsible_modules_with_code: List[Tuple[str, str, str]],
                                         ai_context: Dict[str, Any], feature_modules_dir: str,
                                         attempt_number: int) -> Dict[str, Any]:
        """
        Complete driver module enhancement workflow.

        Args:
            responsible_modules_with_code: List of (feature_name, module_path, module_code) tuples
            ai_context: Context for AI enhancement
            feature_modules_dir: Directory to save attempt modules
            attempt_number: Current attempt number

        Returns:
            Dict with enhancement results and module information
        """
        print(f"🔧 Starting driver module enhancement for {len(responsible_modules_with_code)} modules")
        print(f"📈 Attempt {attempt_number}: Using incremental learning approach")

        try:
            # Phase 1: Combine modules
            combined_code, module_boundaries = self.combine_responsible_modules(responsible_modules_with_code)

            # Phase 2: Enhance combined module
            enhanced_combined_code = self.enhance_combined_module(combined_code, ai_context)

            # Phase 3: Decompose and detect changes
            base_modules = {name: code for name, _, code in responsible_modules_with_code}
            decomposition_results = self.decompose_and_detect_changes(
                base_modules, enhanced_combined_code, module_boundaries
            )

            # Phase 4: Save only changed modules
            save_results = self.save_decomposed_modules(
                decomposition_results, feature_modules_dir, attempt_number
            )

            print(f"💾 Driver module enhancement complete:")
            print(f"   ✅ Modules with changes: {save_results['saved_modules']}")
            print(f"   ➖ Modules unchanged: {save_results['unchanged_modules']}")

            # Prepare results for return
            enhanced_modules = []
            for feature_name, result in decomposition_results.items():
                if result['action'] == 'create_attempt':
                    enhanced_modules.append({
                        'feature_name': feature_name,
                        'module_path': result['module_path'],
                        'enhanced_code': result['enhanced_code'],
                        'code_changed': True,
                        'attempt_number': attempt_number
                    })

            return {
                'success': True,
                'enhanced_modules': enhanced_modules,
                'saved_modules': save_results['saved_modules'],
                'unchanged_modules': save_results['unchanged_modules'],
                'total_modules': len(responsible_modules_with_code),
                'changed_modules_count': len(save_results['saved_modules']),
                'unchanged_modules_count': len(save_results['unchanged_modules']),
                'combined_module_code': combined_code,
                'enhanced_combined_code': enhanced_combined_code
            }

        except Exception as e:
            print(f"❌ Driver module enhancement failed: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'enhanced_modules': [],
                'saved_modules': [],
                'unchanged_modules': [name for name, _, _ in responsible_modules_with_code],
                'total_modules': len(responsible_modules_with_code),
                'changed_modules_count': 0,
                'unchanged_modules_count': len(responsible_modules_with_code)
            }
